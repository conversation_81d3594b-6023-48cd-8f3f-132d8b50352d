from flask import Blueprint, render_template, request, jsonify, make_response
from decorators import login_required
from services.sound_transmission_loss_service import (
    SoundTransmissionLossVerticalService, 
    SoundTransmissionLossWallMountedService
)
from utils.result import success, error, bad_request

# 创建蓝图
sound_transmission_loss_bp = Blueprint('sound_transmission_loss', __name__, url_prefix='/sound_transmission_loss')

# 创建服务实例
vertical_service = SoundTransmissionLossVerticalService()
wall_mounted_service = SoundTransmissionLossWallMountedService()

class SoundTransmissionLossController:
    """隔声量控制器"""
    
    # ========== 垂直入射法页面和API ==========
    
    @staticmethod
    @sound_transmission_loss_bp.route('/vertical_query')
    @login_required
    def vertical_query_page():
        """垂直入射法隔声量查询页面"""
        return render_template('sound_transmission_loss/vertical_query.html')
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/parts')
    @login_required
    def get_vertical_parts():
        """获取垂直入射法零件列表"""
        try:
            parts = vertical_service.get_part_list()
            return success(parts)
        except Exception as e:
            return error(f"获取零件列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/materials')
    @login_required
    def get_vertical_materials():
        """获取垂直入射法材料列表"""
        try:
            part_name = request.args.get('part_name')
            materials = vertical_service.get_material_list(part_name)
            return success(materials)
        except Exception as e:
            return error(f"获取材料列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/weights')
    @login_required
    def get_vertical_weights():
        """获取垂直入射法克重列表"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            
            if not part_name or not material_name:
                return bad_request("请提供零件名称和材料名称")
            
            weights = vertical_service.get_weight_list(part_name, material_name)
            return success(weights)
        except Exception as e:
            return error(f"获取克重列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/transmission_loss_data')
    @login_required
    def get_vertical_transmission_loss_data():
        """获取垂直入射法隔声量数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            data = vertical_service.get_transmission_loss_data(part_name, material_name, weight)
            
            if not data:
                return error("未找到匹配的数据")
            
            return success(data, "数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取隔声量数据失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/multi_weight_comparison', methods=['POST'])
    @login_required
    def get_vertical_multi_weight_comparison():
        """获取垂直入射法多克重对比数据"""
        try:
            data = request.get_json()
            part_name = data.get('part_name')
            material_name = data.get('material_name')
            weights = data.get('weights', [])
            
            if not all([part_name, material_name, weights]):
                return bad_request("请提供完整的对比条件")
            
            if len(weights) < 2:
                return bad_request("至少需要选择2个克重进行对比")
            
            weights = [float(w) for w in weights]
            comparison_data = vertical_service.get_multi_weight_comparison(part_name, material_name, weights)
            
            if not comparison_data:
                return error("未找到匹配的对比数据")
            
            return success(comparison_data, "对比数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取多克重对比数据失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/test_image')
    @login_required
    def get_vertical_test_image():
        """获取垂直入射法测试图片信息"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            image_info = vertical_service.get_test_image_info(part_name, material_name, weight)
            
            if not image_info:
                return error("未找到测试图片信息")
            
            return success(image_info)
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取测试图片信息失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/export_data')
    @login_required
    def export_vertical_data():
        """导出垂直入射法单个数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            csv_content = vertical_service.export_single_data(part_name, material_name, weight)
            
            if not csv_content:
                return error("未找到要导出的数据")
            
            # 创建响应
            response = make_response(csv_content)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=垂直入射法隔声量_{part_name}_{material_name}_{int(weight)}g.csv'
            
            return response
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_loss_bp.route('/api/vertical/export_comparison', methods=['POST'])
    @login_required
    def export_vertical_comparison():
        """导出垂直入射法对比数据"""
        try:
            data = request.get_json()
            part_name = data.get('part_name')
            material_name = data.get('material_name')
            weights = data.get('weights', [])
            
            if not all([part_name, material_name, weights]):
                return bad_request("请提供完整的对比条件")
            
            weights = [float(w) for w in weights]
            csv_content = vertical_service.export_comparison_data(part_name, material_name, weights)
            
            if not csv_content:
                return error("未找到要导出的对比数据")
            
            # 创建响应
            response = make_response(csv_content)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            weights_str = '_'.join([f'{int(w)}g' for w in weights])
            response.headers['Content-Disposition'] = f'attachment; filename=垂直入射法隔声量对比_{part_name}_{material_name}_{weights_str}.csv'
            
            return response
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出对比数据失败: {str(e)}")

    # ========== 壁挂法页面和API ==========

    @staticmethod
    @sound_transmission_loss_bp.route('/wall_mounted_query')
    @login_required
    def wall_mounted_query_page():
        """壁挂法隔声量查询页面"""
        return render_template('sound_transmission_loss/wall_mounted_query.html')

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/parts')
    @login_required
    def get_wall_mounted_parts():
        """获取壁挂法零件列表"""
        try:
            parts = wall_mounted_service.get_part_list()
            return success(parts)
        except Exception as e:
            return error(f"获取零件列表失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/materials')
    @login_required
    def get_wall_mounted_materials():
        """获取壁挂法材料列表"""
        try:
            part_name = request.args.get('part_name')
            materials = wall_mounted_service.get_material_list(part_name)
            return success(materials)
        except Exception as e:
            return error(f"获取材料列表失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/weights')
    @login_required
    def get_wall_mounted_weights():
        """获取壁挂法克重列表"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')

            if not part_name or not material_name:
                return bad_request("请提供零件名称和材料名称")

            weights = wall_mounted_service.get_weight_list(part_name, material_name)
            return success(weights)
        except Exception as e:
            return error(f"获取克重列表失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/transmission_loss_data')
    @login_required
    def get_wall_mounted_transmission_loss_data():
        """获取壁挂法隔声量数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')

            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")

            weight = float(weight)
            data = wall_mounted_service.get_transmission_loss_data(part_name, material_name, weight)

            if not data:
                return error("未找到匹配的数据")

            return success(data, "数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取隔声量数据失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/multi_weight_comparison', methods=['POST'])
    @login_required
    def get_wall_mounted_multi_weight_comparison():
        """获取壁挂法多克重对比数据"""
        try:
            data = request.get_json()
            part_name = data.get('part_name')
            material_name = data.get('material_name')
            weights = data.get('weights', [])

            if not all([part_name, material_name, weights]):
                return bad_request("请提供完整的对比条件")

            if len(weights) < 2:
                return bad_request("至少需要选择2个克重进行对比")

            weights = [float(w) for w in weights]
            comparison_data = wall_mounted_service.get_multi_weight_comparison(part_name, material_name, weights)

            if not comparison_data:
                return error("未找到匹配的对比数据")

            return success(comparison_data, "对比数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取多克重对比数据失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/test_image')
    @login_required
    def get_wall_mounted_test_image():
        """获取壁挂法测试图片信息"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')

            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")

            weight = float(weight)
            image_info = wall_mounted_service.get_test_image_info(part_name, material_name, weight)

            if not image_info:
                return error("未找到测试图片信息")

            return success(image_info)
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取测试图片信息失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/export_data')
    @login_required
    def export_wall_mounted_data():
        """导出壁挂法单个数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')

            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")

            weight = float(weight)
            csv_content = wall_mounted_service.export_single_data(part_name, material_name, weight)

            if not csv_content:
                return error("未找到要导出的数据")

            # 创建响应
            response = make_response(csv_content)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=壁挂法隔声量_{part_name}_{material_name}_{int(weight)}g.csv'

            return response
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")

    @staticmethod
    @sound_transmission_loss_bp.route('/api/wall_mounted/export_comparison', methods=['POST'])
    @login_required
    def export_wall_mounted_comparison():
        """导出壁挂法对比数据"""
        try:
            data = request.get_json()
            part_name = data.get('part_name')
            material_name = data.get('material_name')
            weights = data.get('weights', [])

            if not all([part_name, material_name, weights]):
                return bad_request("请提供完整的对比条件")

            weights = [float(w) for w in weights]
            csv_content = wall_mounted_service.export_comparison_data(part_name, material_name, weights)

            if not csv_content:
                return error("未找到要导出的对比数据")

            # 创建响应
            response = make_response(csv_content)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            weights_str = '_'.join([f'{int(w)}g' for w in weights])
            response.headers['Content-Disposition'] = f'attachment; filename=壁挂法隔声量对比_{part_name}_{material_name}_{weights_str}.csv'

            return response
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出对比数据失败: {str(e)}")
