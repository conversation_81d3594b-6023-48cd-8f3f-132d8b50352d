-- 隔声量功能数据库表设计
-- 基于现有项目架构，新增2个隔声量表
-- 复用现有的零件表、材料表、厂家表

USE nvh_data;

-- 1. 垂直入射法隔声量表
CREATE TABLE sound_transmission_loss_vertical (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_name VARCHAR(100) NOT NULL COMMENT '零件名称',
    material_name VARCHAR(100) NOT NULL COMMENT '材料名称',
    material_manufacturer VARCHAR(100) COMMENT '材料厂家',
    test_institution VARCHAR(100) COMMENT '测试机构',
    thickness DECIMAL(6,2) COMMENT '厚度(mm)',
    weight DECIMAL(8,2) NOT NULL COMMENT '克重(g/m²)',
    
    -- 测试值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    test_value_125 DECIMAL(5,2) COMMENT '125Hz测试值(dB)',
    test_value_160 DECIMAL(5,2) COMMENT '160Hz测试值(dB)',
    test_value_200 DECIMAL(5,2) COMMENT '200Hz测试值(dB)',
    test_value_250 DECIMAL(5,2) COMMENT '250Hz测试值(dB)',
    test_value_315 DECIMAL(5,2) COMMENT '315Hz测试值(dB)',
    test_value_400 DECIMAL(5,2) COMMENT '400Hz测试值(dB)',
    test_value_500 DECIMAL(5,2) COMMENT '500Hz测试值(dB)',
    test_value_630 DECIMAL(5,2) COMMENT '630Hz测试值(dB)',
    test_value_800 DECIMAL(5,2) COMMENT '800Hz测试值(dB)',
    test_value_1000 DECIMAL(5,2) COMMENT '1000Hz测试值(dB)',
    test_value_1250 DECIMAL(5,2) COMMENT '1250Hz测试值(dB)',
    test_value_1600 DECIMAL(5,2) COMMENT '1600Hz测试值(dB)',
    test_value_2000 DECIMAL(5,2) COMMENT '2000Hz测试值(dB)',
    test_value_2500 DECIMAL(5,2) COMMENT '2500Hz测试值(dB)',
    test_value_3150 DECIMAL(5,2) COMMENT '3150Hz测试值(dB)',
    test_value_4000 DECIMAL(5,2) COMMENT '4000Hz测试值(dB)',
    test_value_5000 DECIMAL(5,2) COMMENT '5000Hz测试值(dB)',
    test_value_6300 DECIMAL(5,2) COMMENT '6300Hz测试值(dB)',
    test_value_8000 DECIMAL(5,2) COMMENT '8000Hz测试值(dB)',
    test_value_10000 DECIMAL(5,2) COMMENT '10000Hz测试值(dB)',
    
    -- 目标值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    target_value_125 DECIMAL(5,2) COMMENT '125Hz目标值(dB)',
    target_value_160 DECIMAL(5,2) COMMENT '160Hz目标值(dB)',
    target_value_200 DECIMAL(5,2) COMMENT '200Hz目标值(dB)',
    target_value_250 DECIMAL(5,2) COMMENT '250Hz目标值(dB)',
    target_value_315 DECIMAL(5,2) COMMENT '315Hz目标值(dB)',
    target_value_400 DECIMAL(5,2) COMMENT '400Hz目标值(dB)',
    target_value_500 DECIMAL(5,2) COMMENT '500Hz目标值(dB)',
    target_value_630 DECIMAL(5,2) COMMENT '630Hz目标值(dB)',
    target_value_800 DECIMAL(5,2) COMMENT '800Hz目标值(dB)',
    target_value_1000 DECIMAL(5,2) COMMENT '1000Hz目标值(dB)',
    target_value_1250 DECIMAL(5,2) COMMENT '1250Hz目标值(dB)',
    target_value_1600 DECIMAL(5,2) COMMENT '1600Hz目标值(dB)',
    target_value_2000 DECIMAL(5,2) COMMENT '2000Hz目标值(dB)',
    target_value_2500 DECIMAL(5,2) COMMENT '2500Hz目标值(dB)',
    target_value_3150 DECIMAL(5,2) COMMENT '3150Hz目标值(dB)',
    target_value_4000 DECIMAL(5,2) COMMENT '4000Hz目标值(dB)',
    target_value_5000 DECIMAL(5,2) COMMENT '5000Hz目标值(dB)',
    target_value_6300 DECIMAL(5,2) COMMENT '6300Hz目标值(dB)',
    target_value_8000 DECIMAL(5,2) COMMENT '8000Hz目标值(dB)',
    target_value_10000 DECIMAL(5,2) COMMENT '10000Hz目标值(dB)',
    
    -- 测试信息
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_part_material (part_name, material_name),
    INDEX idx_weight (weight),
    INDEX idx_test_date (test_date)
) COMMENT '垂直入射法隔声量表';

-- 2. 壁挂法隔声量表
CREATE TABLE sound_transmission_loss_wall_mounted (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_name VARCHAR(100) NOT NULL COMMENT '零件名称',
    material_name VARCHAR(100) NOT NULL COMMENT '材料名称',
    material_manufacturer VARCHAR(100) COMMENT '材料厂家',
    test_institution VARCHAR(100) COMMENT '测试机构',
    thickness DECIMAL(6,2) COMMENT '厚度(mm)',
    weight DECIMAL(8,2) NOT NULL COMMENT '克重(g/m²)',
    
    -- 测试值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    test_value_125 DECIMAL(5,2) COMMENT '125Hz测试值(dB)',
    test_value_160 DECIMAL(5,2) COMMENT '160Hz测试值(dB)',
    test_value_200 DECIMAL(5,2) COMMENT '200Hz测试值(dB)',
    test_value_250 DECIMAL(5,2) COMMENT '250Hz测试值(dB)',
    test_value_315 DECIMAL(5,2) COMMENT '315Hz测试值(dB)',
    test_value_400 DECIMAL(5,2) COMMENT '400Hz测试值(dB)',
    test_value_500 DECIMAL(5,2) COMMENT '500Hz测试值(dB)',
    test_value_630 DECIMAL(5,2) COMMENT '630Hz测试值(dB)',
    test_value_800 DECIMAL(5,2) COMMENT '800Hz测试值(dB)',
    test_value_1000 DECIMAL(5,2) COMMENT '1000Hz测试值(dB)',
    test_value_1250 DECIMAL(5,2) COMMENT '1250Hz测试值(dB)',
    test_value_1600 DECIMAL(5,2) COMMENT '1600Hz测试值(dB)',
    test_value_2000 DECIMAL(5,2) COMMENT '2000Hz测试值(dB)',
    test_value_2500 DECIMAL(5,2) COMMENT '2500Hz测试值(dB)',
    test_value_3150 DECIMAL(5,2) COMMENT '3150Hz测试值(dB)',
    test_value_4000 DECIMAL(5,2) COMMENT '4000Hz测试值(dB)',
    test_value_5000 DECIMAL(5,2) COMMENT '5000Hz测试值(dB)',
    test_value_6300 DECIMAL(5,2) COMMENT '6300Hz测试值(dB)',
    test_value_8000 DECIMAL(5,2) COMMENT '8000Hz测试值(dB)',
    test_value_10000 DECIMAL(5,2) COMMENT '10000Hz测试值(dB)',
    
    -- 目标值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    target_value_125 DECIMAL(5,2) COMMENT '125Hz目标值(dB)',
    target_value_160 DECIMAL(5,2) COMMENT '160Hz目标值(dB)',
    target_value_200 DECIMAL(5,2) COMMENT '200Hz目标值(dB)',
    target_value_250 DECIMAL(5,2) COMMENT '250Hz目标值(dB)',
    target_value_315 DECIMAL(5,2) COMMENT '315Hz目标值(dB)',
    target_value_400 DECIMAL(5,2) COMMENT '400Hz目标值(dB)',
    target_value_500 DECIMAL(5,2) COMMENT '500Hz目标值(dB)',
    target_value_630 DECIMAL(5,2) COMMENT '630Hz目标值(dB)',
    target_value_800 DECIMAL(5,2) COMMENT '800Hz目标值(dB)',
    target_value_1000 DECIMAL(5,2) COMMENT '1000Hz目标值(dB)',
    target_value_1250 DECIMAL(5,2) COMMENT '1250Hz目标值(dB)',
    target_value_1600 DECIMAL(5,2) COMMENT '1600Hz目标值(dB)',
    target_value_2000 DECIMAL(5,2) COMMENT '2000Hz目标值(dB)',
    target_value_2500 DECIMAL(5,2) COMMENT '2500Hz目标值(dB)',
    target_value_3150 DECIMAL(5,2) COMMENT '3150Hz目标值(dB)',
    target_value_4000 DECIMAL(5,2) COMMENT '4000Hz目标值(dB)',
    target_value_5000 DECIMAL(5,2) COMMENT '5000Hz目标值(dB)',
    target_value_6300 DECIMAL(5,2) COMMENT '6300Hz目标值(dB)',
    target_value_8000 DECIMAL(5,2) COMMENT '8000Hz目标值(dB)',
    target_value_10000 DECIMAL(5,2) COMMENT '10000Hz目标值(dB)',
    
    -- 测试信息
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_part_material (part_name, material_name),
    INDEX idx_weight (weight),
    INDEX idx_test_date (test_date)
) COMMENT '壁挂法隔声量表';

-- 插入测试数据
-- 垂直入射法隔声量测试数据（示例）
INSERT INTO sound_transmission_loss_vertical (
    part_name, material_name, material_manufacturer, test_institution, thickness, weight,
    test_value_125, test_value_160, test_value_200, test_value_250, test_value_315, test_value_400,
    test_value_500, test_value_630, test_value_800, test_value_1000, test_value_1250, test_value_1600,
    test_value_2000, test_value_2500, test_value_3150, test_value_4000, test_value_5000, test_value_6300,
    test_value_8000, test_value_10000,
    target_value_125, target_value_160, target_value_200, target_value_250, target_value_315, target_value_400,
    target_value_500, target_value_630, target_value_800, target_value_1000, target_value_1250, target_value_1600,
    target_value_2000, target_value_2500, target_value_3150, target_value_4000, target_value_5000, target_value_6300,
    target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, remarks
) VALUES
-- 前围隔音垫 - PET纤维毡 - 不同克重
('前围隔音垫', 'PET纤维毡', '奥托立夫', '上汽通用五菱声学实验室', 10.0, 800,
 15.2, 18.5, 21.3, 24.8, 27.6, 30.2, 32.8, 35.4, 37.9, 40.5, 42.8, 45.2, 47.6, 49.8, 52.1, 54.3, 56.2, 58.0, 59.5, 61.2,
 18.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 46.0, 48.0, 50.0, 52.0, 54.0, 56.0, 58.0, 60.0, 62.0, 64.0, 66.0,
 '2024-01-15', '柳州', '张工', '800g/m²克重垂直入射法隔声量测试'),

('前围隔音垫', 'PET纤维毡', '奥托立夫', '上汽通用五菱声学实验室', 15.0, 1200,
 18.5, 22.1, 25.8, 29.2, 32.5, 35.8, 38.9, 41.7, 44.3, 46.8, 49.2, 51.5, 53.7, 55.8, 57.9, 59.8, 61.5, 63.1, 64.6, 66.0,
 18.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 46.0, 48.0, 50.0, 52.0, 54.0, 56.0, 58.0, 60.0, 62.0, 64.0, 66.0,
 '2024-01-16', '柳州', '张工', '1200g/m²克重垂直入射法隔声量测试'),

('前围隔音垫', 'PET纤维毡', '奥托立夫', '上汽通用五菱声学实验室', 20.0, 1600,
 22.1, 26.3, 30.2, 33.8, 37.1, 40.3, 43.2, 45.9, 48.4, 50.7, 52.9, 55.0, 57.0, 58.9, 60.7, 62.4, 64.0, 65.5, 66.9, 68.2,
 18.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 46.0, 48.0, 50.0, 52.0, 54.0, 56.0, 58.0, 60.0, 62.0, 64.0, 66.0,
 '2024-01-17', '柳州', '张工', '1600g/m²克重垂直入射法隔声量测试');

-- 壁挂法隔声量测试数据（示例）
INSERT INTO sound_transmission_loss_wall_mounted (
    part_name, material_name, material_manufacturer, test_institution, thickness, weight,
    test_value_125, test_value_160, test_value_200, test_value_250, test_value_315, test_value_400,
    test_value_500, test_value_630, test_value_800, test_value_1000, test_value_1250, test_value_1600,
    test_value_2000, test_value_2500, test_value_3150, test_value_4000, test_value_5000, test_value_6300,
    test_value_8000, test_value_10000,
    target_value_125, target_value_160, target_value_200, target_value_250, target_value_315, target_value_400,
    target_value_500, target_value_630, target_value_800, target_value_1000, target_value_1250, target_value_1600,
    target_value_2000, target_value_2500, target_value_3150, target_value_4000, target_value_5000, target_value_6300,
    target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, remarks
) VALUES
-- 前围隔音垫 - PET纤维毡 - 不同克重
('前围隔音垫', 'PET纤维毡', '奥托立夫', '上汽通用五菱声学实验室', 10.0, 800,
 12.8, 16.2, 19.5, 22.9, 25.8, 28.5, 31.1, 33.6, 36.0, 38.3, 40.5, 42.6, 44.6, 46.5, 48.3, 50.0, 51.6, 53.1, 54.5, 55.8,
 15.0, 19.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 45.0, 47.0, 49.0, 51.0, 53.0, 55.0, 57.0, 59.0, 61.0, 63.0,
 '2024-01-18', '柳州', '李工', '800g/m²克重壁挂法隔声量测试'),

('前围隔音垫', 'PET纤维毡', '奥托立夫', '上汽通用五菱声学实验室', 15.0, 1200,
 16.2, 20.1, 23.8, 27.2, 30.3, 33.2, 35.9, 38.4, 40.7, 42.9, 45.0, 47.0, 48.9, 50.7, 52.4, 54.0, 55.5, 56.9, 58.2, 59.4,
 15.0, 19.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 45.0, 47.0, 49.0, 51.0, 53.0, 55.0, 57.0, 59.0, 61.0, 63.0,
 '2024-01-19', '柳州', '李工', '1200g/m²克重壁挂法隔声量测试'),

('前围隔音垫', 'PET纤维毡', '奥托立夫', '上汽通用五菱声学实验室', 20.0, 1600,
 19.5, 24.2, 28.5, 32.4, 35.9, 39.1, 42.0, 44.6, 47.0, 49.2, 51.3, 53.2, 55.0, 56.7, 58.3, 59.8, 61.2, 62.5, 63.7, 64.8,
 15.0, 19.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 45.0, 47.0, 49.0, 51.0, 53.0, 55.0, 57.0, 59.0, 61.0, 63.0,
 '2024-01-20', '柳州', '李工', '1600g/m²克重壁挂法隔声量测试');
