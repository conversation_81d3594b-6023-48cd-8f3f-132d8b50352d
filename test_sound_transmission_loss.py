#!/usr/bin/env python3
"""
隔声量功能测试脚本
"""

import sys
from app import app
from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel,
    MaterialModel,
    MaterialManufacturerModel
)
from models.sound_transmission_loss_models import (
    SoundTransmissionLossVerticalModel,
    SoundTransmissionLossWallMountedModel
)
from services.sound_transmission_loss_service import (
    SoundTransmissionLossVerticalService,
    SoundTransmissionLossWallMountedService
)

def test_models():
    """测试数据模型"""
    print("\n=== 测试数据模型 ===")
    
    with app.app_context():
        try:
            # 测试零件模型
            parts = SoundInsulationPartModel.get_all_parts()
            print(f"✓ 零件模型测试成功，共 {len(parts)} 个零件")
            
            # 测试材料模型
            materials = MaterialModel.get_all_materials()
            print(f"✓ 材料模型测试成功，共 {len(materials)} 个材料")
            
            # 测试厂家模型
            manufacturers = MaterialManufacturerModel.get_all_manufacturers()
            print(f"✓ 厂家模型测试成功，共 {len(manufacturers)} 个厂家")
            
            # 测试垂直入射法模型
            vertical_data = SoundTransmissionLossVerticalModel.query.first()
            if vertical_data:
                print("✓ 垂直入射法模型测试成功")
                test_data = vertical_data.get_test_frequency_data()
                target_data = vertical_data.get_target_frequency_data()
                print(f"  - 125Hz测试值: {test_data.get('125Hz')} dB")
                print(f"  - 1000Hz测试值: {test_data.get('1000Hz')} dB")
                print(f"  - 125Hz目标值: {target_data.get('125Hz')} dB")
            else:
                print("✗ 垂直入射法模型测试失败，无数据")
            
            # 测试壁挂法模型
            wall_mounted_data = SoundTransmissionLossWallMountedModel.query.first()
            if wall_mounted_data:
                print("✓ 壁挂法模型测试成功")
                test_data = wall_mounted_data.get_test_frequency_data()
                target_data = wall_mounted_data.get_target_frequency_data()
                print(f"  - 125Hz测试值: {test_data.get('125Hz')} dB")
                print(f"  - 1000Hz测试值: {test_data.get('1000Hz')} dB")
                print(f"  - 125Hz目标值: {target_data.get('125Hz')} dB")
            else:
                print("✗ 壁挂法模型测试失败，无数据")
            
            # 测试业务方法
            vertical_weights = SoundTransmissionLossVerticalModel.get_weights_by_part_material('前围隔音垫', 'PET纤维毡')
            print(f"✓ 垂直入射法克重查询测试成功，前围隔音垫-PET纤维毡有 {len(vertical_weights)} 个克重")
            
            wall_mounted_weights = SoundTransmissionLossWallMountedModel.get_weights_by_part_material('前围隔音垫', 'PET纤维毡')
            print(f"✓ 壁挂法克重查询测试成功，前围隔音垫-PET纤维毡有 {len(wall_mounted_weights)} 个克重")
            
            # 测试数据查询
            vertical_data = SoundTransmissionLossVerticalModel.get_data_by_conditions('前围隔音垫', 'PET纤维毡', 800)
            if vertical_data:
                print("✓ 垂直入射法数据查询测试成功")
            else:
                print("✗ 垂直入射法数据查询测试失败")
            
            wall_mounted_data = SoundTransmissionLossWallMountedModel.get_data_by_conditions('前围隔音垫', 'PET纤维毡', 800)
            if wall_mounted_data:
                print("✓ 壁挂法数据查询测试成功")
            else:
                print("✗ 壁挂法数据查询测试失败")
            
        except Exception as e:
            print(f"✗ 模型测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_services():
    """测试服务层"""
    print("\n=== 测试业务服务 ===")
    
    with app.app_context():
        try:
            # 测试垂直入射法服务
            vertical_service = SoundTransmissionLossVerticalService()
            
            # 测试零件列表
            parts = vertical_service.get_part_list()
            print(f"✓ 垂直入射法零件列表获取成功，共 {len(parts)} 个零件")
            
            # 测试材料列表
            materials = vertical_service.get_material_list('前围隔音垫')
            print(f"✓ 垂直入射法材料列表获取成功，共 {len(materials)} 个材料")
            
            # 测试克重列表
            weights = vertical_service.get_weight_list('前围隔音垫', 'PET纤维毡')
            print(f"✓ 垂直入射法克重列表获取成功，共 {len(weights)} 个克重")
            
            # 测试隔声量数据获取
            data = vertical_service.get_transmission_loss_data('前围隔音垫', 'PET纤维毡', 800)
            if data:
                print("✓ 垂直入射法隔声量数据获取成功")
                print(f"  - 基础信息: {data['basic_info']['part_name']}")
                print(f"  - 表格数据行数: {len(data['table_data'])}")
                print(f"  - 图表频率点数: {len(data['chart_data']['frequencies'])}")
            else:
                print("✗ 垂直入射法隔声量数据获取失败")
            
            # 测试多克重对比
            comparison_data = vertical_service.get_multi_weight_comparison('前围隔音垫', 'PET纤维毡', [800, 1200, 1600])
            if comparison_data:
                print("✓ 垂直入射法多克重对比数据获取成功")
                print(f"  - 对比数据数量: {comparison_data['data_count']}")
                print(f"  - 对比克重: {comparison_data['basic_info']['weights']}")
            else:
                print("✗ 垂直入射法多克重对比数据获取失败")
            
            # 测试壁挂法服务
            wall_mounted_service = SoundTransmissionLossWallMountedService()
            
            # 测试零件列表
            parts = wall_mounted_service.get_part_list()
            print(f"✓ 壁挂法零件列表获取成功，共 {len(parts)} 个零件")
            
            # 测试材料列表
            materials = wall_mounted_service.get_material_list('前围隔音垫')
            print(f"✓ 壁挂法材料列表获取成功，共 {len(materials)} 个材料")
            
            # 测试克重列表
            weights = wall_mounted_service.get_weight_list('前围隔音垫', 'PET纤维毡')
            print(f"✓ 壁挂法克重列表获取成功，共 {len(weights)} 个克重")
            
            # 测试隔声量数据获取
            data = wall_mounted_service.get_transmission_loss_data('前围隔音垫', 'PET纤维毡', 800)
            if data:
                print("✓ 壁挂法隔声量数据获取成功")
                print(f"  - 基础信息: {data['basic_info']['part_name']}")
                print(f"  - 表格数据行数: {len(data['table_data'])}")
                print(f"  - 图表频率点数: {len(data['chart_data']['frequencies'])}")
            else:
                print("✗ 壁挂法隔声量数据获取失败")
            
            # 测试多克重对比
            comparison_data = wall_mounted_service.get_multi_weight_comparison('前围隔音垫', 'PET纤维毡', [800, 1200, 1600])
            if comparison_data:
                print("✓ 壁挂法多克重对比数据获取成功")
                print(f"  - 对比数据数量: {comparison_data['data_count']}")
                print(f"  - 对比克重: {comparison_data['basic_info']['weights']}")
            else:
                print("✗ 壁挂法多克重对比数据获取失败")
            
        except Exception as e:
            print(f"✗ 服务测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_export():
    """测试导出功能"""
    print("\n=== 测试导出功能 ===")
    
    with app.app_context():
        try:
            # 测试垂直入射法导出
            vertical_service = SoundTransmissionLossVerticalService()
            
            # 测试单个数据导出
            csv_content = vertical_service.export_single_data('前围隔音垫', 'PET纤维毡', 800)
            if csv_content and len(csv_content) > 0:
                print("✓ 垂直入射法单个数据导出成功")
                print(f"  - CSV内容长度: {len(csv_content)} 字符")
            else:
                print("✗ 垂直入射法单个数据导出失败")
            
            # 测试对比数据导出
            csv_content = vertical_service.export_comparison_data('前围隔音垫', 'PET纤维毡', [800, 1200, 1600])
            if csv_content and len(csv_content) > 0:
                print("✓ 垂直入射法对比数据导出成功")
                print(f"  - CSV内容长度: {len(csv_content)} 字符")
            else:
                print("✗ 垂直入射法对比数据导出失败")
            
            # 测试壁挂法导出
            wall_mounted_service = SoundTransmissionLossWallMountedService()
            
            # 测试单个数据导出
            csv_content = wall_mounted_service.export_single_data('前围隔音垫', 'PET纤维毡', 800)
            if csv_content and len(csv_content) > 0:
                print("✓ 壁挂法单个数据导出成功")
                print(f"  - CSV内容长度: {len(csv_content)} 字符")
            else:
                print("✗ 壁挂法单个数据导出失败")
            
            # 测试对比数据导出
            csv_content = wall_mounted_service.export_comparison_data('前围隔音垫', 'PET纤维毡', [800, 1200, 1600])
            if csv_content and len(csv_content) > 0:
                print("✓ 壁挂法对比数据导出成功")
                print(f"  - CSV内容长度: {len(csv_content)} 字符")
            else:
                print("✗ 壁挂法对比数据导出失败")
            
        except Exception as e:
            print(f"✗ 导出功能测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_data_integrity():
    """测试数据完整性"""
    print("\n=== 测试数据完整性 ===")
    
    with app.app_context():
        try:
            # 检查垂直入射法数据
            vertical_count = SoundTransmissionLossVerticalModel.query.count()
            print(f"✓ 垂直入射法数据记录数: {vertical_count}")
            
            # 检查壁挂法数据
            wall_mounted_count = SoundTransmissionLossWallMountedModel.query.count()
            print(f"✓ 壁挂法数据记录数: {wall_mounted_count}")
            
            # 检查数据关联性
            from models import db
            vertical_parts = db.session.query(SoundInsulationPartModel).join(
                SoundTransmissionLossVerticalModel,
                SoundInsulationPartModel.part_name == SoundTransmissionLossVerticalModel.part_name
            ).distinct().all()
            print(f"✓ 有垂直入射法数据的零件数: {len(vertical_parts)}")

            wall_mounted_parts = db.session.query(SoundInsulationPartModel).join(
                SoundTransmissionLossWallMountedModel,
                SoundInsulationPartModel.part_name == SoundTransmissionLossWallMountedModel.part_name
            ).distinct().all()
            print(f"✓ 有壁挂法数据的零件数: {len(wall_mounted_parts)}")
            
            # 检查频率数据完整性
            vertical_data = SoundTransmissionLossVerticalModel.query.first()
            if vertical_data:
                test_data = vertical_data.get_test_frequency_data()
                target_data = vertical_data.get_target_frequency_data()
                test_count = sum(1 for v in test_data.values() if v is not None)
                target_count = sum(1 for v in target_data.values() if v is not None)
                print(f"✓ 垂直入射法频率数据完整性: 测试值 {test_count}/20, 目标值 {target_count}/20")
            
            wall_mounted_data = SoundTransmissionLossWallMountedModel.query.first()
            if wall_mounted_data:
                test_data = wall_mounted_data.get_test_frequency_data()
                target_data = wall_mounted_data.get_target_frequency_data()
                test_count = sum(1 for v in test_data.values() if v is not None)
                target_count = sum(1 for v in target_data.values() if v is not None)
                print(f"✓ 壁挂法频率数据完整性: 测试值 {test_count}/20, 目标值 {target_count}/20")
            
        except Exception as e:
            print(f"✗ 数据完整性测试失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("隔声量功能测试开始...")
    
    # 运行所有测试
    test_models()
    test_services()
    test_export()
    test_data_integrity()
    
    print("\n隔声量功能测试完成！")

if __name__ == '__main__':
    main()
