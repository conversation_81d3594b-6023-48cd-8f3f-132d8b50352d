#!/usr/bin/env python3
"""
隔声量功能数据库初始化脚本
"""

import os
import sys
from app import app
from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel,
    MaterialModel,
    MaterialManufacturerModel
)
from models.sound_transmission_loss_models import (
    SoundTransmissionLossVerticalModel,
    SoundTransmissionLossWallMountedModel
)

def init_database():
    """初始化数据库"""
    print("开始初始化隔声量功能数据库...")
    
    with app.app_context():
        try:
            # 创建表
            print("创建数据表...")
            db.create_all()
            print("✓ 数据表创建成功")
            
            # 检查是否已有隔声量数据
            if SoundTransmissionLossVerticalModel.query.first():
                print("⚠ 隔声量数据已存在，跳过初始化")
                return
            
            # 检查基础数据是否存在，不存在则插入
            if not SoundInsulationPartModel.query.first():
                print("插入基础数据...")
                insert_basic_data()
            else:
                print("基础数据已存在，跳过插入")

            # 插入隔声量测试数据
            insert_test_data()
            
            print("✓ 隔声量功能数据库初始化完成")
            
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            db.session.rollback()
            raise

def insert_basic_data():
    """插入基础数据"""
    print("插入基础数据...")
    
    # 零件数据
    parts_data = [
        {'part_name': '前围隔音垫', 'description': '发动机舱与驾驶室之间的隔音材料'},
        {'part_name': '地毯', 'description': '车内地板隔音吸音材料'},
        {'part_name': '顶棚', 'description': '车顶内饰隔音吸音材料'},
        {'part_name': '门板内饰', 'description': '车门内侧隔音吸音材料'},
        {'part_name': '后围隔音垫', 'description': '后备箱与驾驶室之间的隔音材料'}
    ]
    
    for part_data in parts_data:
        part = SoundInsulationPartModel(**part_data)
        db.session.add(part)
    
    # 材料数据
    materials_data = [
        {'material_name': 'PET纤维毡', 'thickness': 10.0, 'weight': 800, 'description': '聚酯纤维毡材料'},
        {'material_name': 'PET纤维毡', 'thickness': 15.0, 'weight': 1200, 'description': '聚酯纤维毡材料'},
        {'material_name': 'PET纤维毡', 'thickness': 20.0, 'weight': 1600, 'description': '聚酯纤维毡材料'},
        {'material_name': '玻璃纤维毡', 'thickness': 12.0, 'weight': 1000, 'description': '玻璃纤维毡材料'},
        {'material_name': '玻璃纤维毡', 'thickness': 18.0, 'weight': 1500, 'description': '玻璃纤维毡材料'},
        {'material_name': '聚氨酯泡沫', 'thickness': 25.0, 'weight': 600, 'description': '聚氨酯发泡材料'},
        {'material_name': '聚氨酯泡沫', 'thickness': 30.0, 'weight': 800, 'description': '聚氨酯发泡材料'}
    ]
    
    for material_data in materials_data:
        material = MaterialModel(**material_data)
        db.session.add(material)
    
    # 厂家数据
    manufacturers_data = [
        {'manufacturer_name': '奥托立夫', 'description': '汽车安全系统供应商'},
        {'manufacturer_name': '佛吉亚', 'description': '汽车零部件供应商'},
        {'manufacturer_name': '延锋', 'description': '汽车内饰供应商'},
        {'manufacturer_name': '李尔', 'description': '汽车座椅和内饰供应商'},
        {'manufacturer_name': '麦格纳', 'description': '汽车零部件供应商'}
    ]
    
    for manufacturer_data in manufacturers_data:
        manufacturer = MaterialManufacturerModel(**manufacturer_data)
        db.session.add(manufacturer)
    
    db.session.commit()
    print("✓ 基础数据插入成功")

def insert_test_data():
    """插入测试数据"""
    print("插入测试数据...")
    
    # 垂直入射法测试数据
    vertical_test_data = [
        {
            'part_name': '前围隔音垫',
            'material_name': 'PET纤维毡',
            'material_manufacturer': '奥托立夫',
            'test_institution': '上汽通用五菱声学实验室',
            'thickness': 10.0,
            'weight': 800,
            'test_values': [15.2, 18.5, 21.3, 24.8, 27.6, 30.2, 32.8, 35.4, 37.9, 40.5, 42.8, 45.2, 47.6, 49.8, 52.1, 54.3, 56.2, 58.0, 59.5, 61.2],
            'target_values': [18.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 46.0, 48.0, 50.0, 52.0, 54.0, 56.0, 58.0, 60.0, 62.0, 64.0, 66.0],
            'test_date': '2024-01-15',
            'test_location': '柳州',
            'test_engineer': '张工',
            'remarks': '800g/m²克重垂直入射法隔声量测试'
        },
        {
            'part_name': '前围隔音垫',
            'material_name': 'PET纤维毡',
            'material_manufacturer': '奥托立夫',
            'test_institution': '上汽通用五菱声学实验室',
            'thickness': 15.0,
            'weight': 1200,
            'test_values': [18.5, 22.1, 25.8, 29.2, 32.5, 35.8, 38.9, 41.7, 44.3, 46.8, 49.2, 51.5, 53.7, 55.8, 57.9, 59.8, 61.5, 63.1, 64.6, 66.0],
            'target_values': [18.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 46.0, 48.0, 50.0, 52.0, 54.0, 56.0, 58.0, 60.0, 62.0, 64.0, 66.0],
            'test_date': '2024-01-16',
            'test_location': '柳州',
            'test_engineer': '张工',
            'remarks': '1200g/m²克重垂直入射法隔声量测试'
        },
        {
            'part_name': '前围隔音垫',
            'material_name': 'PET纤维毡',
            'material_manufacturer': '奥托立夫',
            'test_institution': '上汽通用五菱声学实验室',
            'thickness': 20.0,
            'weight': 1600,
            'test_values': [22.1, 26.3, 30.2, 33.8, 37.1, 40.3, 43.2, 45.9, 48.4, 50.7, 52.9, 55.0, 57.0, 58.9, 60.7, 62.4, 64.0, 65.5, 66.9, 68.2],
            'target_values': [18.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 46.0, 48.0, 50.0, 52.0, 54.0, 56.0, 58.0, 60.0, 62.0, 64.0, 66.0],
            'test_date': '2024-01-17',
            'test_location': '柳州',
            'test_engineer': '张工',
            'remarks': '1600g/m²克重垂直入射法隔声量测试'
        }
    ]
    
    frequencies = ['125', '160', '200', '250', '315', '400', '500', '630', '800', 
                   '1000', '1250', '1600', '2000', '2500', '3150', '4000', '5000', '6300', '8000', '10000']
    
    for data in vertical_test_data:
        vertical_record = SoundTransmissionLossVerticalModel(
            part_name=data['part_name'],
            material_name=data['material_name'],
            material_manufacturer=data['material_manufacturer'],
            test_institution=data['test_institution'],
            thickness=data['thickness'],
            weight=data['weight'],
            test_date=data['test_date'],
            test_location=data['test_location'],
            test_engineer=data['test_engineer'],
            remarks=data['remarks']
        )
        
        # 设置测试值
        for i, freq in enumerate(frequencies):
            setattr(vertical_record, f'test_value_{freq}', data['test_values'][i])
            setattr(vertical_record, f'target_value_{freq}', data['target_values'][i])
        
        db.session.add(vertical_record)
    
    # 壁挂法测试数据
    wall_mounted_test_data = [
        {
            'part_name': '前围隔音垫',
            'material_name': 'PET纤维毡',
            'material_manufacturer': '奥托立夫',
            'test_institution': '上汽通用五菱声学实验室',
            'thickness': 10.0,
            'weight': 800,
            'test_values': [12.8, 16.2, 19.5, 22.9, 25.8, 28.5, 31.1, 33.6, 36.0, 38.3, 40.5, 42.6, 44.6, 46.5, 48.3, 50.0, 51.6, 53.1, 54.5, 55.8],
            'target_values': [15.0, 19.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 45.0, 47.0, 49.0, 51.0, 53.0, 55.0, 57.0, 59.0, 61.0, 63.0],
            'test_date': '2024-01-18',
            'test_location': '柳州',
            'test_engineer': '李工',
            'remarks': '800g/m²克重壁挂法隔声量测试'
        },
        {
            'part_name': '前围隔音垫',
            'material_name': 'PET纤维毡',
            'material_manufacturer': '奥托立夫',
            'test_institution': '上汽通用五菱声学实验室',
            'thickness': 15.0,
            'weight': 1200,
            'test_values': [16.2, 20.1, 23.8, 27.2, 30.3, 33.2, 35.9, 38.4, 40.7, 42.9, 45.0, 47.0, 48.9, 50.7, 52.4, 54.0, 55.5, 56.9, 58.2, 59.4],
            'target_values': [15.0, 19.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 45.0, 47.0, 49.0, 51.0, 53.0, 55.0, 57.0, 59.0, 61.0, 63.0],
            'test_date': '2024-01-19',
            'test_location': '柳州',
            'test_engineer': '李工',
            'remarks': '1200g/m²克重壁挂法隔声量测试'
        },
        {
            'part_name': '前围隔音垫',
            'material_name': 'PET纤维毡',
            'material_manufacturer': '奥托立夫',
            'test_institution': '上汽通用五菱声学实验室',
            'thickness': 20.0,
            'weight': 1600,
            'test_values': [19.5, 24.2, 28.5, 32.4, 35.9, 39.1, 42.0, 44.6, 47.0, 49.2, 51.3, 53.2, 55.0, 56.7, 58.3, 59.8, 61.2, 62.5, 63.7, 64.8],
            'target_values': [15.0, 19.0, 22.0, 25.0, 28.0, 31.0, 34.0, 37.0, 40.0, 43.0, 45.0, 47.0, 49.0, 51.0, 53.0, 55.0, 57.0, 59.0, 61.0, 63.0],
            'test_date': '2024-01-20',
            'test_location': '柳州',
            'test_engineer': '李工',
            'remarks': '1600g/m²克重壁挂法隔声量测试'
        }
    ]
    
    for data in wall_mounted_test_data:
        wall_mounted_record = SoundTransmissionLossWallMountedModel(
            part_name=data['part_name'],
            material_name=data['material_name'],
            material_manufacturer=data['material_manufacturer'],
            test_institution=data['test_institution'],
            thickness=data['thickness'],
            weight=data['weight'],
            test_date=data['test_date'],
            test_location=data['test_location'],
            test_engineer=data['test_engineer'],
            remarks=data['remarks']
        )
        
        # 设置测试值
        for i, freq in enumerate(frequencies):
            setattr(wall_mounted_record, f'test_value_{freq}', data['test_values'][i])
            setattr(wall_mounted_record, f'target_value_{freq}', data['target_values'][i])
        
        db.session.add(wall_mounted_record)
    
    db.session.commit()
    print("✓ 测试数据插入成功")

if __name__ == '__main__':
    init_database()
