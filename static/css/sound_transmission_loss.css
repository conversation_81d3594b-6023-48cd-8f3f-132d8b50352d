/* 隔声量模块样式文件 */

/* 基础样式 */
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.card-header h5 {
    color: #5a5c69;
    font-weight: 600;
    margin: 0;
}

/* 查询条件区域 */
.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

/* 按钮样式 */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-outline-secondary {
    color: #858796;
    border-color: #858796;
}

.btn-outline-secondary:hover {
    background-color: #858796;
    border-color: #858796;
}

.btn-outline-info {
    color: #36b9cc;
    border-color: #36b9cc;
}

.btn-outline-info:hover {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-outline-success {
    color: #1cc88a;
    border-color: #1cc88a;
}

.btn-outline-success:hover {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table-dark {
    background-color: #5a5c69;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(0, 0, 0, 0.075);
}

.table th {
    font-weight: 600;
    border-top: none;
    vertical-align: middle;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
}

/* 表格响应式 */
.table-responsive {
    border-radius: 0.35rem;
}

/* 状态标识 */
.text-success {
    color: #1cc88a !important;
}

.text-danger {
    color: #e74a3b !important;
}

.text-muted {
    color: #858796 !important;
}

/* 基础信息卡片 */
.bg-light {
    background-color: #f8f9fc !important;
}

.card-title {
    color: #5a5c69;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 图表容器 */
#chart-container,
#comparison-chart-container {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    background-color: #fff;
}

/* 加载指示器 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 空状态样式 */
#empty-state .fa-3x {
    font-size: 3em;
    color: #d1d3e2;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.modal-title {
    color: #5a5c69;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

/* 复选框样式 */
.form-check {
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-label {
    font-weight: 500;
    color: #5a5c69;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
}

.bg-secondary {
    background-color: #858796 !important;
}

/* 图片样式 */
#test-image {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-header .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-group-sm .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    #chart-container,
    #comparison-chart-container {
        height: 300px !important;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #5a5c69;
    color: #fff;
    border-radius: 0.35rem;
    padding: 0.5rem 0.75rem;
}

/* 对比表格特殊样式 */
#comparison-table {
    font-size: 0.875rem;
}

#comparison-table th {
    font-size: 0.8rem;
    padding: 0.5rem;
    text-align: center;
}

#comparison-table td {
    padding: 0.5rem;
    text-align: center;
}

/* 频率列固定 */
#comparison-table th:first-child,
#comparison-table td:first-child {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 10;
    border-right: 2px solid #e3e6f0;
    font-weight: 600;
}

#comparison-table thead th:first-child {
    background-color: #5a5c69;
    color: #fff;
}

/* 数值格式化 */
.number-cell {
    font-family: 'Courier New', monospace;
    text-align: right;
}

/* 高亮行 */
.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

/* 图标间距 */
.fas, .far, .fab {
    margin-right: 0.25rem;
}
