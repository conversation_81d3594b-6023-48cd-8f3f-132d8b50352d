/**
 * 壁挂法隔声量查询页面JavaScript
 */

// 全局变量
let currentData = null;
let currentChart = null;
let comparisonChart = null;
let availableWeights = [];

// API基础路径
const API_BASE = '/sound_transmission_loss/api/wall_mounted';

// DOM元素
const elements = {
    partSelect: document.getElementById('part-select'),
    materialSelect: document.getElementById('material-select'),
    weightSelect: document.getElementById('weight-select'),
    searchBtn: document.getElementById('search-btn'),
    multiCompareBtn: document.getElementById('multi-compare-btn'),
    resultsCard: document.getElementById('results-card'),
    comparisonResultsCard: document.getElementById('comparison-results-card'),
    emptyState: document.getElementById('empty-state'),
    loadingIndicator: document.getElementById('loading-indicator'),
    basicInfo: document.getElementById('basic-info'),
    dataTable: document.getElementById('data-table').getElementsByTagName('tbody')[0],
    chartContainer: document.getElementById('chart-container'),
    comparisonChartContainer: document.getElementById('comparison-chart-container'),
    viewImageBtn: document.getElementById('view-image-btn'),
    exportBtn: document.getElementById('export-btn'),
    exportComparisonBtn: document.getElementById('export-comparison-btn'),
    comparisonCount: document.getElementById('comparison-count'),
    comparisonInfo: document.getElementById('comparison-info'),
    comparisonTable: document.getElementById('comparison-table'),
    multiWeightModal: new bootstrap.Modal(document.getElementById('multiWeightModal')),
    imageModal: new bootstrap.Modal(document.getElementById('imageModal')),
    weightCheckboxes: document.getElementById('weight-checkboxes'),
    confirmComparisonBtn: document.getElementById('confirm-comparison-btn')
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    bindEvents();
});

// 初始化页面
function initializePage() {
    loadParts();
}

// 绑定事件
function bindEvents() {
    // 下拉框变化事件
    elements.partSelect.addEventListener('change', onPartChange);
    elements.materialSelect.addEventListener('change', onMaterialChange);
    elements.weightSelect.addEventListener('change', onWeightChange);
    
    // 按钮点击事件
    elements.searchBtn.addEventListener('click', performSearch);
    elements.multiCompareBtn.addEventListener('click', showMultiWeightModal);
    elements.viewImageBtn.addEventListener('click', viewTestImage);
    elements.exportBtn.addEventListener('click', exportSingleData);
    elements.exportComparisonBtn.addEventListener('click', exportComparisonData);
    elements.confirmComparisonBtn.addEventListener('click', performComparison);
    
    // 复选框变化事件
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('weight-checkbox')) {
            updateConfirmButton();
        }
    });
}

// 加载零件列表
async function loadParts() {
    try {
        showLoading(true);
        const response = await fetch(`${API_BASE}/parts`);
        const result = await response.json();
        
        if (result.success) {
            populateSelect(elements.partSelect, result.data, 'name', 'name');
        } else {
            showError('加载零件列表失败: ' + result.message);
        }
    } catch (error) {
        showError('加载零件列表失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 零件选择变化
async function onPartChange() {
    const partName = elements.partSelect.value;
    
    // 重置后续选择
    resetSelect(elements.materialSelect, '请先选择零件');
    resetSelect(elements.weightSelect, '请先选择材料');
    updateButtonStates();
    
    if (!partName) return;
    
    try {
        showLoading(true);
        const response = await fetch(`${API_BASE}/materials?part_name=${encodeURIComponent(partName)}`);
        const result = await response.json();
        
        if (result.success) {
            populateSelect(elements.materialSelect, result.data, 'name', 'name');
            elements.materialSelect.disabled = false;
        } else {
            showError('加载材料列表失败: ' + result.message);
        }
    } catch (error) {
        showError('加载材料列表失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 材料选择变化
async function onMaterialChange() {
    const partName = elements.partSelect.value;
    const materialName = elements.materialSelect.value;
    
    // 重置克重选择
    resetSelect(elements.weightSelect, '请先选择材料');
    updateButtonStates();
    
    if (!partName || !materialName) return;
    
    try {
        showLoading(true);
        const response = await fetch(`${API_BASE}/weights?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}`);
        const result = await response.json();
        
        if (result.success) {
            availableWeights = result.data;
            populateSelect(elements.weightSelect, result.data, 'weight', 'display');
            elements.weightSelect.disabled = false;
        } else {
            showError('加载克重列表失败: ' + result.message);
        }
    } catch (error) {
        showError('加载克重列表失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 克重选择变化
function onWeightChange() {
    updateButtonStates();
}

// 更新按钮状态
function updateButtonStates() {
    const hasSelection = elements.partSelect.value && elements.materialSelect.value && elements.weightSelect.value;
    elements.searchBtn.disabled = !hasSelection;
    elements.multiCompareBtn.disabled = !elements.materialSelect.value || availableWeights.length < 2;
}

// 执行查询
async function performSearch() {
    const partName = elements.partSelect.value;
    const materialName = elements.materialSelect.value;
    const weight = elements.weightSelect.value;
    
    if (!partName || !materialName || !weight) {
        showError('请选择完整的查询条件');
        return;
    }
    
    try {
        showLoading(true);
        hideAllResults();
        
        const response = await fetch(`${API_BASE}/transmission_loss_data?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${weight}`);
        const result = await response.json();
        
        if (result.success) {
            currentData = result.data;
            displayResults(result.data);
            showSuccess('数据查询成功');
        } else {
            showError('查询失败: ' + result.message);
            showEmptyState();
        }
    } catch (error) {
        showError('查询失败: ' + error.message);
        showEmptyState();
    } finally {
        showLoading(false);
    }
}

// 显示结果
function displayResults(data) {
    // 显示基础信息
    displayBasicInfo(data.basic_info);
    
    // 显示数据表格
    displayDataTable(data.table_data);
    
    // 显示图表
    displayChart(data.chart_data);
    
    // 显示结果卡片
    elements.resultsCard.style.display = 'block';
    elements.emptyState.style.display = 'none';
    elements.comparisonResultsCard.style.display = 'none';
}

// 显示基础信息
function displayBasicInfo(basicInfo) {
    const infoHtml = `
        <div class="col-md-3">
            <strong>零件名称:</strong><br>
            <span class="text-muted">${basicInfo.part_name || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>材料名称:</strong><br>
            <span class="text-muted">${basicInfo.material_name || '-'}</span>
        </div>
        <div class="col-md-2">
            <strong>厚度:</strong><br>
            <span class="text-muted">${basicInfo.thickness ? basicInfo.thickness + 'mm' : '-'}</span>
        </div>
        <div class="col-md-2">
            <strong>克重:</strong><br>
            <span class="text-muted">${basicInfo.weight}g/m²</span>
        </div>
        <div class="col-md-2">
            <strong>测试日期:</strong><br>
            <span class="text-muted">${basicInfo.test_date || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>材料厂家:</strong><br>
            <span class="text-muted">${basicInfo.material_manufacturer || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>测试机构:</strong><br>
            <span class="text-muted">${basicInfo.test_institution || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>测试地点:</strong><br>
            <span class="text-muted">${basicInfo.test_location || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>测试工程师:</strong><br>
            <span class="text-muted">${basicInfo.test_engineer || '-'}</span>
        </div>
    `;
    elements.basicInfo.innerHTML = infoHtml;
}

// 显示数据表格
function displayDataTable(tableData) {
    elements.dataTable.innerHTML = '';

    tableData.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td><strong>${row.frequency}</strong></td>
            <td class="number-cell">${row.test_value !== null ? row.test_value.toFixed(2) : '-'}</td>
            <td class="number-cell">${row.target_value !== null ? row.target_value.toFixed(2) : '-'}</td>
            <td><span class="${row.status_class}">${row.status}</span></td>
        `;
        elements.dataTable.appendChild(tr);
    });
}

// 显示图表
function displayChart(chartData) {
    if (currentChart) {
        currentChart.dispose();
    }

    currentChart = echarts.init(elements.chartContainer);

    const option = {
        title: {
            text: '壁挂法隔声量频率响应曲线',
            left: 'center',
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            },
            formatter: function(params) {
                let html = `<strong>${params[0].axisValue}</strong><br/>`;
                params.forEach(param => {
                    const value = param.value !== null ? param.value.toFixed(2) + ' dB' : '无数据';
                    html += `${param.marker} ${param.seriesName}: ${value}<br/>`;
                });
                return html;
            }
        },
        legend: {
            data: ['测试值', '目标值'],
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: chartData.frequencies,
            axisLabel: {
                rotate: 45,
                fontSize: 10
            },
            name: '频率',
            nameLocation: 'middle',
            nameGap: 30
        },
        yAxis: {
            type: 'value',
            name: '隔声量 (dB)',
            nameLocation: 'middle',
            nameGap: 50,
            axisLabel: {
                formatter: '{value} dB'
            }
        },
        series: [
            {
                name: '测试值',
                type: 'line',
                data: chartData.test_values,
                lineStyle: {
                    color: '#4e73df',
                    width: 2
                },
                itemStyle: {
                    color: '#4e73df'
                },
                symbol: 'circle',
                symbolSize: 6,
                connectNulls: false
            },
            {
                name: '目标值',
                type: 'line',
                data: chartData.target_values,
                lineStyle: {
                    color: '#1cc88a',
                    width: 2,
                    type: 'dashed'
                },
                itemStyle: {
                    color: '#1cc88a'
                },
                symbol: 'triangle',
                symbolSize: 6,
                connectNulls: false
            }
        ]
    };

    currentChart.setOption(option);

    // 响应式调整
    window.addEventListener('resize', function() {
        if (currentChart) {
            currentChart.resize();
        }
    });
}

// 显示多克重选择模态框
function showMultiWeightModal() {
    if (availableWeights.length < 2) {
        showError('至少需要2个克重才能进行对比');
        return;
    }

    // 生成复选框
    let checkboxHtml = '';
    availableWeights.forEach(weight => {
        checkboxHtml += `
            <div class="form-check">
                <input class="form-check-input weight-checkbox" type="checkbox" value="${weight.weight}" id="weight-${weight.weight}">
                <label class="form-check-label" for="weight-${weight.weight}">
                    ${weight.display}
                </label>
            </div>
        `;
    });

    elements.weightCheckboxes.innerHTML = checkboxHtml;
    elements.confirmComparisonBtn.disabled = true;
    elements.multiWeightModal.show();
}

// 更新确认按钮状态
function updateConfirmButton() {
    const checkedBoxes = document.querySelectorAll('.weight-checkbox:checked');
    elements.confirmComparisonBtn.disabled = checkedBoxes.length < 2;
}

// 执行多克重对比
async function performComparison() {
    const checkedBoxes = document.querySelectorAll('.weight-checkbox:checked');
    const selectedWeights = Array.from(checkedBoxes).map(cb => parseFloat(cb.value));

    if (selectedWeights.length < 2) {
        showError('请至少选择2个克重进行对比');
        return;
    }

    const partName = elements.partSelect.value;
    const materialName = elements.materialSelect.value;

    try {
        showLoading(true);
        elements.multiWeightModal.hide();
        hideAllResults();

        const response = await fetch(`${API_BASE}/multi_weight_comparison`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                part_name: partName,
                material_name: materialName,
                weights: selectedWeights
            })
        });

        const result = await response.json();

        if (result.success) {
            displayComparisonResults(result.data);
            showSuccess('对比数据获取成功');
        } else {
            showError('获取对比数据失败: ' + result.message);
            showEmptyState();
        }
    } catch (error) {
        showError('获取对比数据失败: ' + error.message);
        showEmptyState();
    } finally {
        showLoading(false);
    }
}

// 显示对比结果
function displayComparisonResults(data) {
    // 显示对比信息
    displayComparisonInfo(data.basic_info);

    // 显示对比表格
    displayComparisonTable(data.table_data, data.basic_info.weights);

    // 显示对比图表
    displayComparisonChart(data.chart_data);

    // 更新计数
    elements.comparisonCount.textContent = `${data.data_count} 个克重`;

    // 显示对比结果卡片
    elements.comparisonResultsCard.style.display = 'block';
    elements.resultsCard.style.display = 'none';
    elements.emptyState.style.display = 'none';
}

// 显示对比信息
function displayComparisonInfo(basicInfo) {
    const weightsStr = basicInfo.weights.map(w => `${w}g/m²`).join(', ');
    const infoHtml = `
        <div class="col-md-3">
            <strong>零件名称:</strong><br>
            <span class="text-muted">${basicInfo.part_name || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>材料名称:</strong><br>
            <span class="text-muted">${basicInfo.material_name || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>材料厂家:</strong><br>
            <span class="text-muted">${basicInfo.material_manufacturer || '-'}</span>
        </div>
        <div class="col-md-3">
            <strong>测试机构:</strong><br>
            <span class="text-muted">${basicInfo.test_institution || '-'}</span>
        </div>
        <div class="col-12">
            <strong>对比克重:</strong><br>
            <span class="text-muted">${weightsStr}</span>
        </div>
    `;
    elements.comparisonInfo.innerHTML = infoHtml;
}

// 显示对比表格
function displayComparisonTable(tableData, weights) {
    // 构建表头
    let headerHtml = '<tr><th>频率</th>';
    weights.forEach(weight => {
        headerHtml += `<th colspan="2">${weight}g/m²</th>`;
    });
    headerHtml += '</tr><tr><th></th>';
    weights.forEach(() => {
        headerHtml += '<th>测试值(dB)</th><th>目标值(dB)</th>';
    });
    headerHtml += '</tr>';

    elements.comparisonTable.querySelector('thead').innerHTML = headerHtml;

    // 构建表格内容
    const tbody = elements.comparisonTable.querySelector('tbody');
    tbody.innerHTML = '';

    tableData.forEach(row => {
        const tr = document.createElement('tr');
        let rowHtml = `<td><strong>${row.frequency}</strong></td>`;

        weights.forEach(weight => {
            const weightKey = `weight_${weight}`;
            const testValue = row[`${weightKey}_test`];
            const targetValue = row[`${weightKey}_target`];

            rowHtml += `
                <td class="number-cell">${testValue !== null ? testValue.toFixed(2) : '-'}</td>
                <td class="number-cell">${targetValue !== null ? targetValue.toFixed(2) : '-'}</td>
            `;
        });

        tr.innerHTML = rowHtml;
        tbody.appendChild(tr);
    });
}

// 显示对比图表
function displayComparisonChart(chartData) {
    if (comparisonChart) {
        comparisonChart.dispose();
    }

    comparisonChart = echarts.init(elements.comparisonChartContainer);

    const option = {
        title: {
            text: '壁挂法多克重隔声量对比曲线',
            left: 'center',
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            },
            formatter: function(params) {
                let html = `<strong>${params[0].axisValue}</strong><br/>`;
                params.forEach(param => {
                    const value = param.value !== null ? param.value.toFixed(2) + ' dB' : '无数据';
                    html += `${param.marker} ${param.seriesName}: ${value}<br/>`;
                });
                return html;
            }
        },
        legend: {
            data: chartData.series.map(s => s.name),
            top: 30,
            type: 'scroll'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: chartData.frequencies,
            axisLabel: {
                rotate: 45,
                fontSize: 10
            },
            name: '频率',
            nameLocation: 'middle',
            nameGap: 30
        },
        yAxis: {
            type: 'value',
            name: '隔声量 (dB)',
            nameLocation: 'middle',
            nameGap: 50,
            axisLabel: {
                formatter: '{value} dB'
            }
        },
        series: chartData.series.map((series, index) => ({
            ...series,
            connectNulls: false,
            symbolSize: 6
        }))
    };

    comparisonChart.setOption(option);

    // 响应式调整
    window.addEventListener('resize', function() {
        if (comparisonChart) {
            comparisonChart.resize();
        }
    });
}

// 查看测试图片
async function viewTestImage() {
    if (!currentData) return;

    const partName = elements.partSelect.value;
    const materialName = elements.materialSelect.value;
    const weight = elements.weightSelect.value;

    try {
        const response = await fetch(`${API_BASE}/test_image?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${weight}`);
        const result = await response.json();

        if (result.success) {
            const imageInfo = result.data;
            displayImageModal(imageInfo);
        } else {
            showError('获取测试图片失败: ' + result.message);
        }
    } catch (error) {
        showError('获取测试图片失败: ' + error.message);
    }
}

// 显示图片模态框
function displayImageModal(imageInfo) {
    const basicInfo = imageInfo.basic_info;
    const infoHtml = `
        <div class="row">
            <div class="col-md-6">
                <strong>零件:</strong> ${basicInfo.part_name}<br>
                <strong>材料:</strong> ${basicInfo.material_name}<br>
                <strong>克重:</strong> ${basicInfo.weight}g/m²
            </div>
            <div class="col-md-6">
                <strong>测试日期:</strong> ${basicInfo.test_date || '-'}<br>
                <strong>测试工程师:</strong> ${basicInfo.test_engineer || '-'}<br>
                <strong>测试地点:</strong> ${basicInfo.test_location || '-'}
            </div>
        </div>
    `;

    document.getElementById('image-info').innerHTML = infoHtml;

    const testImage = document.getElementById('test-image');
    const noImage = document.getElementById('no-image');

    if (imageInfo.has_image && imageInfo.image_path) {
        testImage.src = imageInfo.image_path;
        testImage.style.display = 'block';
        noImage.style.display = 'none';
    } else {
        testImage.style.display = 'none';
        noImage.style.display = 'block';
    }

    elements.imageModal.show();
}

// 导出单个数据
async function exportSingleData() {
    const partName = elements.partSelect.value;
    const materialName = elements.materialSelect.value;
    const weight = elements.weightSelect.value;

    if (!partName || !materialName || !weight) {
        showError('请先查询数据');
        return;
    }

    try {
        const response = await fetch(`${API_BASE}/export_data?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${weight}`);

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `壁挂法隔声量_${partName}_${materialName}_${weight}g.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showSuccess('数据导出成功');
        } else {
            const result = await response.json();
            showError('导出失败: ' + result.message);
        }
    } catch (error) {
        showError('导出失败: ' + error.message);
    }
}

// 导出对比数据
async function exportComparisonData() {
    const checkedBoxes = document.querySelectorAll('.weight-checkbox:checked');
    if (checkedBoxes.length === 0) {
        showError('请先进行多克重对比');
        return;
    }

    const selectedWeights = Array.from(checkedBoxes).map(cb => parseFloat(cb.value));
    const partName = elements.partSelect.value;
    const materialName = elements.materialSelect.value;

    try {
        const response = await fetch(`${API_BASE}/export_comparison`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                part_name: partName,
                material_name: materialName,
                weights: selectedWeights
            })
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            const weightsStr = selectedWeights.map(w => `${w}g`).join('_');
            a.href = url;
            a.download = `壁挂法隔声量对比_${partName}_${materialName}_${weightsStr}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showSuccess('对比数据导出成功');
        } else {
            const result = await response.json();
            showError('导出失败: ' + result.message);
        }
    } catch (error) {
        showError('导出失败: ' + error.message);
    }
}

// 工具函数
function populateSelect(selectElement, data, valueField, textField) {
    // 清空现有选项（保留第一个默认选项）
    while (selectElement.children.length > 1) {
        selectElement.removeChild(selectElement.lastChild);
    }

    // 添加新选项
    data.forEach(item => {
        const option = document.createElement('option');
        option.value = item[valueField];
        option.textContent = item[textField];
        selectElement.appendChild(option);
    });
}

function resetSelect(selectElement, placeholder) {
    selectElement.innerHTML = `<option value="">${placeholder}</option>`;
    selectElement.disabled = true;
}

function showLoading(show) {
    elements.loadingIndicator.style.display = show ? 'block' : 'none';
}

function hideAllResults() {
    elements.resultsCard.style.display = 'none';
    elements.comparisonResultsCard.style.display = 'none';
    elements.emptyState.style.display = 'none';
}

function showEmptyState() {
    elements.emptyState.style.display = 'block';
    elements.resultsCard.style.display = 'none';
    elements.comparisonResultsCard.style.display = 'none';
}

function showSuccess(message) {
    // 使用Bootstrap的Toast或者简单的alert
    // 这里使用简单的alert，实际项目中可以使用更好的通知组件
    console.log('Success:', message);
}

function showError(message) {
    // 使用Bootstrap的Toast或者简单的alert
    // 这里使用简单的alert，实际项目中可以使用更好的通知组件
    alert('错误: ' + message);
    console.error('Error:', message);
}
