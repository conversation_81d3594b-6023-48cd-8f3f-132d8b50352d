from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel,
    MaterialModel,
    MaterialManufacturerModel
)
from models.sound_transmission_loss_models import (
    SoundTransmissionLossVerticalModel,
    SoundTransmissionLossWallMountedModel
)
import csv
import io

class SoundTransmissionLossVerticalService:
    """垂直入射法隔声量业务逻辑服务"""
    
    def get_part_list(self):
        """获取零件列表"""
        # 获取有垂直入射法数据的零件列表
        parts = db.session.query(SoundInsulationPartModel).join(
            SoundTransmissionLossVerticalModel,
            SoundInsulationPartModel.part_name == SoundTransmissionLossVerticalModel.part_name
        ).distinct().order_by(SoundInsulationPartModel.part_name).all()
        return [{'name': part.part_name, 'description': part.description} for part in parts]

    def get_material_list(self, part_name=None):
        """获取材料列表"""
        if part_name:
            # 根据零件获取有垂直入射法数据的材料列表
            materials = db.session.query(MaterialModel).join(
                SoundTransmissionLossVerticalModel,
                MaterialModel.material_name == SoundTransmissionLossVerticalModel.material_name
            ).filter(
                SoundTransmissionLossVerticalModel.part_name == part_name
            ).distinct().order_by(MaterialModel.material_name).all()
        else:
            materials = MaterialModel.get_all_materials()

        return [{'name': material.material_name, 'description': material.description} for material in materials]
    
    def get_weight_list(self, part_name, material_name):
        """获取克重列表"""
        weights = SoundTransmissionLossVerticalModel.get_weights_by_part_material(part_name, material_name)
        return [{'weight': weight, 'display': f"{weight}g/m²"} for weight in weights]
    
    def get_transmission_loss_data(self, part_name, material_name, weight):
        """获取隔声量数据"""
        data = SoundTransmissionLossVerticalModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None
        
        # 获取基础信息
        basic_info = data.get_basic_info()
        
        # 获取频率数据
        test_data = data.get_test_frequency_data()
        target_data = data.get_target_frequency_data()
        
        # 构建表格数据
        table_data = []
        frequencies = SoundTransmissionLossVerticalModel.FREQUENCIES
        
        for freq in frequencies:
            freq_key = f'{freq}Hz'
            test_value = test_data.get(freq_key)
            target_value = target_data.get(freq_key)
            
            # 判断达标状态
            status = '未知'
            status_class = 'text-muted'
            if test_value is not None and target_value is not None:
                if test_value >= target_value:
                    status = '达标'
                    status_class = 'text-success'
                else:
                    status = '未达标'
                    status_class = 'text-danger'
            
            table_data.append({
                'frequency': freq_key,
                'test_value': test_value,
                'target_value': target_value,
                'status': status,
                'status_class': status_class
            })
        
        # 构建图表数据
        chart_data = {
            'frequencies': [f'{freq}Hz' for freq in frequencies],
            'test_values': [test_data.get(f'{freq}Hz') for freq in frequencies],
            'target_values': [target_data.get(f'{freq}Hz') for freq in frequencies]
        }
        
        return {
            'basic_info': basic_info,
            'table_data': table_data,
            'chart_data': chart_data,
            'test_data': test_data,
            'target_data': target_data
        }
    
    def get_multi_weight_comparison(self, part_name, material_name, weights):
        """获取多克重对比数据"""
        data_list = SoundTransmissionLossVerticalModel.get_multi_weight_data(part_name, material_name, weights)
        
        if not data_list:
            return None
        
        # 基础信息
        first_data = data_list[0]
        basic_info = {
            'part_name': first_data.part_name,
            'material_name': first_data.material_name,
            'material_manufacturer': first_data.material_manufacturer,
            'test_institution': first_data.test_institution,
            'weights': [float(data.weight) for data in data_list]
        }
        
        # 构建对比表格数据
        frequencies = SoundTransmissionLossVerticalModel.FREQUENCIES
        table_data = []
        
        for freq in frequencies:
            freq_key = f'{freq}Hz'
            row = {'frequency': freq_key}
            
            for data in data_list:
                weight_key = f'weight_{int(data.weight)}'
                test_data = data.get_test_frequency_data()
                target_data = data.get_target_frequency_data()
                
                row[f'{weight_key}_test'] = test_data.get(freq_key)
                row[f'{weight_key}_target'] = target_data.get(freq_key)
            
            table_data.append(row)
        
        # 构建对比图表数据
        chart_data = {
            'frequencies': [f'{freq}Hz' for freq in frequencies],
            'series': []
        }
        
        for data in data_list:
            weight = int(data.weight)
            test_data = data.get_test_frequency_data()
            target_data = data.get_target_frequency_data()
            
            # 测试值系列
            chart_data['series'].append({
                'name': f'{weight}g/m² 测试值',
                'type': 'line',
                'data': [test_data.get(f'{freq}Hz') for freq in frequencies],
                'lineStyle': {'type': 'solid'},
                'symbol': 'circle'
            })
            
            # 目标值系列
            chart_data['series'].append({
                'name': f'{weight}g/m² 目标值',
                'type': 'line',
                'data': [target_data.get(f'{freq}Hz') for freq in frequencies],
                'lineStyle': {'type': 'dashed'},
                'symbol': 'triangle'
            })
        
        return {
            'basic_info': basic_info,
            'table_data': table_data,
            'chart_data': chart_data,
            'data_count': len(data_list)
        }
    
    def get_test_image_info(self, part_name, material_name, weight):
        """获取测试图片信息"""
        data = SoundTransmissionLossVerticalModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None
        
        return data.get_image_info()
    
    def export_single_data(self, part_name, material_name, weight):
        """导出单个数据为CSV"""
        data = SoundTransmissionLossVerticalModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入标题
        writer.writerow(['垂直入射法隔声量数据导出'])
        writer.writerow([])
        
        # 写入基础信息
        basic_info = data.get_basic_info()
        writer.writerow(['基础信息'])
        writer.writerow(['零件名称', basic_info['part_name']])
        writer.writerow(['材料名称', basic_info['material_name']])
        writer.writerow(['材料厂家', basic_info.get('material_manufacturer', '')])
        writer.writerow(['测试机构', basic_info.get('test_institution', '')])
        writer.writerow(['厚度(mm)', basic_info.get('thickness', '')])
        writer.writerow(['克重(g/m²)', basic_info['weight']])
        writer.writerow(['测试日期', basic_info.get('test_date', '')])
        writer.writerow(['测试地点', basic_info.get('test_location', '')])
        writer.writerow(['测试工程师', basic_info.get('test_engineer', '')])
        writer.writerow([])
        
        # 写入频率数据
        writer.writerow(['频率数据'])
        writer.writerow(['频率', '测试值(dB)', '目标值(dB)'])
        
        test_data = data.get_test_frequency_data()
        target_data = data.get_target_frequency_data()
        
        for freq in SoundTransmissionLossVerticalModel.FREQUENCIES:
            freq_key = f'{freq}Hz'
            writer.writerow([
                freq_key,
                test_data.get(freq_key, ''),
                target_data.get(freq_key, '')
            ])
        
        csv_content = output.getvalue()
        output.close()
        
        return csv_content
    
    def export_comparison_data(self, part_name, material_name, weights):
        """导出对比数据为CSV"""
        data_list = SoundTransmissionLossVerticalModel.get_multi_weight_data(part_name, material_name, weights)
        
        if not data_list:
            return None
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入标题
        writer.writerow(['垂直入射法隔声量多克重对比数据导出'])
        writer.writerow([])
        
        # 写入基础信息
        first_data = data_list[0]
        writer.writerow(['基础信息'])
        writer.writerow(['零件名称', first_data.part_name])
        writer.writerow(['材料名称', first_data.material_name])
        writer.writerow(['材料厂家', first_data.material_manufacturer or ''])
        writer.writerow(['测试机构', first_data.test_institution or ''])
        writer.writerow(['对比克重', ', '.join([f'{int(data.weight)}g/m²' for data in data_list])])
        writer.writerow([])
        
        # 构建表头
        header = ['频率']
        for data in data_list:
            weight = int(data.weight)
            header.extend([f'{weight}g/m² 测试值(dB)', f'{weight}g/m² 目标值(dB)'])
        writer.writerow(header)
        
        # 写入频率数据
        for freq in SoundTransmissionLossVerticalModel.FREQUENCIES:
            freq_key = f'{freq}Hz'
            row = [freq_key]
            
            for data in data_list:
                test_data = data.get_test_frequency_data()
                target_data = data.get_target_frequency_data()
                row.extend([
                    test_data.get(freq_key, ''),
                    target_data.get(freq_key, '')
                ])
            
            writer.writerow(row)
        
        csv_content = output.getvalue()
        output.close()

        return csv_content


class SoundTransmissionLossWallMountedService:
    """壁挂法隔声量业务逻辑服务"""

    def get_part_list(self):
        """获取零件列表"""
        # 获取有壁挂法数据的零件列表
        parts = db.session.query(SoundInsulationPartModel).join(
            SoundTransmissionLossWallMountedModel,
            SoundInsulationPartModel.part_name == SoundTransmissionLossWallMountedModel.part_name
        ).distinct().order_by(SoundInsulationPartModel.part_name).all()
        return [{'name': part.part_name, 'description': part.description} for part in parts]

    def get_material_list(self, part_name=None):
        """获取材料列表"""
        if part_name:
            # 根据零件获取有壁挂法数据的材料列表
            materials = db.session.query(MaterialModel).join(
                SoundTransmissionLossWallMountedModel,
                MaterialModel.material_name == SoundTransmissionLossWallMountedModel.material_name
            ).filter(
                SoundTransmissionLossWallMountedModel.part_name == part_name
            ).distinct().order_by(MaterialModel.material_name).all()
        else:
            materials = MaterialModel.get_all_materials()

        return [{'name': material.material_name, 'description': material.description} for material in materials]

    def get_weight_list(self, part_name, material_name):
        """获取克重列表"""
        weights = SoundTransmissionLossWallMountedModel.get_weights_by_part_material(part_name, material_name)
        return [{'weight': weight, 'display': f"{weight}g/m²"} for weight in weights]

    def get_transmission_loss_data(self, part_name, material_name, weight):
        """获取隔声量数据"""
        data = SoundTransmissionLossWallMountedModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None

        # 获取基础信息
        basic_info = data.get_basic_info()

        # 获取频率数据
        test_data = data.get_test_frequency_data()
        target_data = data.get_target_frequency_data()

        # 构建表格数据
        table_data = []
        frequencies = SoundTransmissionLossWallMountedModel.FREQUENCIES

        for freq in frequencies:
            freq_key = f'{freq}Hz'
            test_value = test_data.get(freq_key)
            target_value = target_data.get(freq_key)

            # 判断达标状态
            status = '未知'
            status_class = 'text-muted'
            if test_value is not None and target_value is not None:
                if test_value >= target_value:
                    status = '达标'
                    status_class = 'text-success'
                else:
                    status = '未达标'
                    status_class = 'text-danger'

            table_data.append({
                'frequency': freq_key,
                'test_value': test_value,
                'target_value': target_value,
                'status': status,
                'status_class': status_class
            })

        # 构建图表数据
        chart_data = {
            'frequencies': [f'{freq}Hz' for freq in frequencies],
            'test_values': [test_data.get(f'{freq}Hz') for freq in frequencies],
            'target_values': [target_data.get(f'{freq}Hz') for freq in frequencies]
        }

        return {
            'basic_info': basic_info,
            'table_data': table_data,
            'chart_data': chart_data,
            'test_data': test_data,
            'target_data': target_data
        }

    def get_multi_weight_comparison(self, part_name, material_name, weights):
        """获取多克重对比数据"""
        data_list = SoundTransmissionLossWallMountedModel.get_multi_weight_data(part_name, material_name, weights)

        if not data_list:
            return None

        # 基础信息
        first_data = data_list[0]
        basic_info = {
            'part_name': first_data.part_name,
            'material_name': first_data.material_name,
            'material_manufacturer': first_data.material_manufacturer,
            'test_institution': first_data.test_institution,
            'weights': [float(data.weight) for data in data_list]
        }

        # 构建对比表格数据
        frequencies = SoundTransmissionLossWallMountedModel.FREQUENCIES
        table_data = []

        for freq in frequencies:
            freq_key = f'{freq}Hz'
            row = {'frequency': freq_key}

            for data in data_list:
                weight_key = f'weight_{int(data.weight)}'
                test_data = data.get_test_frequency_data()
                target_data = data.get_target_frequency_data()

                row[f'{weight_key}_test'] = test_data.get(freq_key)
                row[f'{weight_key}_target'] = target_data.get(freq_key)

            table_data.append(row)

        # 构建对比图表数据
        chart_data = {
            'frequencies': [f'{freq}Hz' for freq in frequencies],
            'series': []
        }

        for data in data_list:
            weight = int(data.weight)
            test_data = data.get_test_frequency_data()
            target_data = data.get_target_frequency_data()

            # 测试值系列
            chart_data['series'].append({
                'name': f'{weight}g/m² 测试值',
                'type': 'line',
                'data': [test_data.get(f'{freq}Hz') for freq in frequencies],
                'lineStyle': {'type': 'solid'},
                'symbol': 'circle'
            })

            # 目标值系列
            chart_data['series'].append({
                'name': f'{weight}g/m² 目标值',
                'type': 'line',
                'data': [target_data.get(f'{freq}Hz') for freq in frequencies],
                'lineStyle': {'type': 'dashed'},
                'symbol': 'triangle'
            })

        return {
            'basic_info': basic_info,
            'table_data': table_data,
            'chart_data': chart_data,
            'data_count': len(data_list)
        }

    def get_test_image_info(self, part_name, material_name, weight):
        """获取测试图片信息"""
        data = SoundTransmissionLossWallMountedModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None

        return data.get_image_info()

    def export_single_data(self, part_name, material_name, weight):
        """导出单个数据为CSV"""
        data = SoundTransmissionLossWallMountedModel.get_data_by_conditions(part_name, material_name, weight)
        if not data:
            return None

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入标题
        writer.writerow(['壁挂法隔声量数据导出'])
        writer.writerow([])

        # 写入基础信息
        basic_info = data.get_basic_info()
        writer.writerow(['基础信息'])
        writer.writerow(['零件名称', basic_info['part_name']])
        writer.writerow(['材料名称', basic_info['material_name']])
        writer.writerow(['材料厂家', basic_info.get('material_manufacturer', '')])
        writer.writerow(['测试机构', basic_info.get('test_institution', '')])
        writer.writerow(['厚度(mm)', basic_info.get('thickness', '')])
        writer.writerow(['克重(g/m²)', basic_info['weight']])
        writer.writerow(['测试日期', basic_info.get('test_date', '')])
        writer.writerow(['测试地点', basic_info.get('test_location', '')])
        writer.writerow(['测试工程师', basic_info.get('test_engineer', '')])
        writer.writerow([])

        # 写入频率数据
        writer.writerow(['频率数据'])
        writer.writerow(['频率', '测试值(dB)', '目标值(dB)'])

        test_data = data.get_test_frequency_data()
        target_data = data.get_target_frequency_data()

        for freq in SoundTransmissionLossWallMountedModel.FREQUENCIES:
            freq_key = f'{freq}Hz'
            writer.writerow([
                freq_key,
                test_data.get(freq_key, ''),
                target_data.get(freq_key, '')
            ])

        csv_content = output.getvalue()
        output.close()

        return csv_content

    def export_comparison_data(self, part_name, material_name, weights):
        """导出对比数据为CSV"""
        data_list = SoundTransmissionLossWallMountedModel.get_multi_weight_data(part_name, material_name, weights)

        if not data_list:
            return None

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入标题
        writer.writerow(['壁挂法隔声量多克重对比数据导出'])
        writer.writerow([])

        # 写入基础信息
        first_data = data_list[0]
        writer.writerow(['基础信息'])
        writer.writerow(['零件名称', first_data.part_name])
        writer.writerow(['材料名称', first_data.material_name])
        writer.writerow(['材料厂家', first_data.material_manufacturer or ''])
        writer.writerow(['测试机构', first_data.test_institution or ''])
        writer.writerow(['对比克重', ', '.join([f'{int(data.weight)}g/m²' for data in data_list])])
        writer.writerow([])

        # 构建表头
        header = ['频率']
        for data in data_list:
            weight = int(data.weight)
            header.extend([f'{weight}g/m² 测试值(dB)', f'{weight}g/m² 目标值(dB)'])
        writer.writerow(header)

        # 写入频率数据
        for freq in SoundTransmissionLossWallMountedModel.FREQUENCIES:
            freq_key = f'{freq}Hz'
            row = [freq_key]

            for data in data_list:
                test_data = data.get_test_frequency_data()
                target_data = data.get_target_frequency_data()
                row.extend([
                    test_data.get(freq_key, ''),
                    target_data.get(freq_key, '')
                ])

            writer.writerow(row)

        csv_content = output.getvalue()
        output.close()

        return csv_content
