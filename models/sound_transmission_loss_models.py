from models.base_model import BaseModel, db
from sqlalchemy import and_

class SoundTransmissionLossVerticalModel(BaseModel):
    """垂直入射法隔声量模型"""
    __tablename__ = 'sound_transmission_loss_vertical'
    
    part_name = db.Column(db.String(100), nullable=False, comment='零件名称')
    material_name = db.Column(db.String(100), nullable=False, comment='材料名称')
    material_manufacturer = db.Column(db.String(100), comment='材料厂家')
    test_institution = db.Column(db.String(100), comment='测试机构')
    thickness = db.Column(db.Numeric(6, 2), comment='厚度(mm)')
    weight = db.Column(db.Numeric(8, 2), nullable=False, comment='克重(g/m²)')
    
    # 测试值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    test_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz测试值(dB)')
    test_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz测试值(dB)')
    test_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz测试值(dB)')
    test_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz测试值(dB)')
    test_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz测试值(dB)')
    test_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz测试值(dB)')
    test_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz测试值(dB)')
    test_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz测试值(dB)')
    test_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz测试值(dB)')
    test_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz测试值(dB)')
    test_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz测试值(dB)')
    test_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz测试值(dB)')
    test_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz测试值(dB)')
    test_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz测试值(dB)')
    test_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz测试值(dB)')
    test_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz测试值(dB)')
    test_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz测试值(dB)')
    test_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz测试值(dB)')
    test_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz测试值(dB)')
    test_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz测试值(dB)')
    
    # 目标值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    target_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz目标值(dB)')
    target_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz目标值(dB)')
    target_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz目标值(dB)')
    target_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz目标值(dB)')
    target_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz目标值(dB)')
    target_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz目标值(dB)')
    target_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz目标值(dB)')
    target_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz目标值(dB)')
    target_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz目标值(dB)')
    target_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz目标值(dB)')
    target_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz目标值(dB)')
    target_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz目标值(dB)')
    target_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz目标值(dB)')
    target_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz目标值(dB)')
    target_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz目标值(dB)')
    target_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz目标值(dB)')
    target_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz目标值(dB)')
    target_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz目标值(dB)')
    target_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz目标值(dB)')
    target_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz目标值(dB)')
    
    # 测试信息
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    remarks = db.Column(db.Text, comment='备注')
    
    def __repr__(self):
        return f'<SoundTransmissionLossVerticalModel {self.part_name}-{self.material_name}-{self.weight}g/m²>'
    
    # 频率列表
    FREQUENCIES = ['125', '160', '200', '250', '315', '400', '500', '630', '800', 
                   '1000', '1250', '1600', '2000', '2500', '3150', '4000', '5000', '6300', '8000', '10000']
    
    def get_test_frequency_data(self):
        """获取测试频率数据字典"""
        data = {}
        for freq in self.FREQUENCIES:
            test_attr = f'test_value_{freq}'
            if hasattr(self, test_attr):
                value = getattr(self, test_attr)
                data[f'{freq}Hz'] = float(value) if value is not None else None
        return data
    
    def get_target_frequency_data(self):
        """获取目标频率数据字典"""
        data = {}
        for freq in self.FREQUENCIES:
            target_attr = f'target_value_{freq}'
            if hasattr(self, target_attr):
                value = getattr(self, target_attr)
                data[f'{freq}Hz'] = float(value) if value is not None else None
        return data
    
    def get_basic_info(self):
        """获取基础信息"""
        return {
            'part_name': self.part_name,
            'material_name': self.material_name,
            'material_manufacturer': self.material_manufacturer,
            'test_institution': self.test_institution,
            'thickness': float(self.thickness) if self.thickness else None,
            'weight': float(self.weight),
            'test_date': self.test_date.strftime('%Y-%m-%d') if self.test_date else None,
            'test_location': self.test_location,
            'test_engineer': self.test_engineer,
            'remarks': self.remarks
        }
    
    def get_image_info(self):
        """获取图片信息"""
        return {
            'image_path': self.test_image_path,
            'has_image': bool(self.test_image_path),
            'basic_info': self.get_basic_info()
        }
    
    @classmethod
    def get_weights_by_part_material(cls, part_name, material_name):
        """根据零件和材料获取克重列表"""
        weights = db.session.query(cls.weight).filter(
            and_(cls.part_name == part_name, cls.material_name == material_name)
        ).distinct().order_by(cls.weight).all()
        return [float(w[0]) for w in weights]
    
    @classmethod
    def get_data_by_conditions(cls, part_name, material_name, weight):
        """根据条件获取数据"""
        return cls.query.filter(
            and_(
                cls.part_name == part_name,
                cls.material_name == material_name,
                cls.weight == weight
            )
        ).first()
    
    @classmethod
    def get_multi_weight_data(cls, part_name, material_name, weights):
        """获取多克重数据"""
        return cls.query.filter(
            and_(
                cls.part_name == part_name,
                cls.material_name == material_name,
                cls.weight.in_(weights)
            )
        ).order_by(cls.weight).all()


class SoundTransmissionLossWallMountedModel(BaseModel):
    """壁挂法隔声量模型"""
    __tablename__ = 'sound_transmission_loss_wall_mounted'

    part_name = db.Column(db.String(100), nullable=False, comment='零件名称')
    material_name = db.Column(db.String(100), nullable=False, comment='材料名称')
    material_manufacturer = db.Column(db.String(100), comment='材料厂家')
    test_institution = db.Column(db.String(100), comment='测试机构')
    thickness = db.Column(db.Numeric(6, 2), comment='厚度(mm)')
    weight = db.Column(db.Numeric(8, 2), nullable=False, comment='克重(g/m²)')

    # 测试值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    test_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz测试值(dB)')
    test_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz测试值(dB)')
    test_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz测试值(dB)')
    test_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz测试值(dB)')
    test_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz测试值(dB)')
    test_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz测试值(dB)')
    test_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz测试值(dB)')
    test_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz测试值(dB)')
    test_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz测试值(dB)')
    test_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz测试值(dB)')
    test_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz测试值(dB)')
    test_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz测试值(dB)')
    test_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz测试值(dB)')
    test_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz测试值(dB)')
    test_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz测试值(dB)')
    test_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz测试值(dB)')
    test_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz测试值(dB)')
    test_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz测试值(dB)')
    test_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz测试值(dB)')
    test_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz测试值(dB)')

    # 目标值 (19个频率点: 125Hz-10000Hz) - 隔声量单位为dB
    target_value_125 = db.Column(db.Numeric(5, 2), comment='125Hz目标值(dB)')
    target_value_160 = db.Column(db.Numeric(5, 2), comment='160Hz目标值(dB)')
    target_value_200 = db.Column(db.Numeric(5, 2), comment='200Hz目标值(dB)')
    target_value_250 = db.Column(db.Numeric(5, 2), comment='250Hz目标值(dB)')
    target_value_315 = db.Column(db.Numeric(5, 2), comment='315Hz目标值(dB)')
    target_value_400 = db.Column(db.Numeric(5, 2), comment='400Hz目标值(dB)')
    target_value_500 = db.Column(db.Numeric(5, 2), comment='500Hz目标值(dB)')
    target_value_630 = db.Column(db.Numeric(5, 2), comment='630Hz目标值(dB)')
    target_value_800 = db.Column(db.Numeric(5, 2), comment='800Hz目标值(dB)')
    target_value_1000 = db.Column(db.Numeric(5, 2), comment='1000Hz目标值(dB)')
    target_value_1250 = db.Column(db.Numeric(5, 2), comment='1250Hz目标值(dB)')
    target_value_1600 = db.Column(db.Numeric(5, 2), comment='1600Hz目标值(dB)')
    target_value_2000 = db.Column(db.Numeric(5, 2), comment='2000Hz目标值(dB)')
    target_value_2500 = db.Column(db.Numeric(5, 2), comment='2500Hz目标值(dB)')
    target_value_3150 = db.Column(db.Numeric(5, 2), comment='3150Hz目标值(dB)')
    target_value_4000 = db.Column(db.Numeric(5, 2), comment='4000Hz目标值(dB)')
    target_value_5000 = db.Column(db.Numeric(5, 2), comment='5000Hz目标值(dB)')
    target_value_6300 = db.Column(db.Numeric(5, 2), comment='6300Hz目标值(dB)')
    target_value_8000 = db.Column(db.Numeric(5, 2), comment='8000Hz目标值(dB)')
    target_value_10000 = db.Column(db.Numeric(5, 2), comment='10000Hz目标值(dB)')

    # 测试信息
    test_date = db.Column(db.Date, comment='测试日期')
    test_location = db.Column(db.String(100), comment='测试地点')
    test_engineer = db.Column(db.String(50), comment='测试工程师')
    test_image_path = db.Column(db.String(500), comment='测试图片路径')
    remarks = db.Column(db.Text, comment='备注')

    def __repr__(self):
        return f'<SoundTransmissionLossWallMountedModel {self.part_name}-{self.material_name}-{self.weight}g/m²>'

    # 频率列表
    FREQUENCIES = ['125', '160', '200', '250', '315', '400', '500', '630', '800',
                   '1000', '1250', '1600', '2000', '2500', '3150', '4000', '5000', '6300', '8000', '10000']

    def get_test_frequency_data(self):
        """获取测试频率数据字典"""
        data = {}
        for freq in self.FREQUENCIES:
            test_attr = f'test_value_{freq}'
            if hasattr(self, test_attr):
                value = getattr(self, test_attr)
                data[f'{freq}Hz'] = float(value) if value is not None else None
        return data

    def get_target_frequency_data(self):
        """获取目标频率数据字典"""
        data = {}
        for freq in self.FREQUENCIES:
            target_attr = f'target_value_{freq}'
            if hasattr(self, target_attr):
                value = getattr(self, target_attr)
                data[f'{freq}Hz'] = float(value) if value is not None else None
        return data

    def get_basic_info(self):
        """获取基础信息"""
        return {
            'part_name': self.part_name,
            'material_name': self.material_name,
            'material_manufacturer': self.material_manufacturer,
            'test_institution': self.test_institution,
            'thickness': float(self.thickness) if self.thickness else None,
            'weight': float(self.weight),
            'test_date': self.test_date.strftime('%Y-%m-%d') if self.test_date else None,
            'test_location': self.test_location,
            'test_engineer': self.test_engineer,
            'remarks': self.remarks
        }

    def get_image_info(self):
        """获取图片信息"""
        return {
            'image_path': self.test_image_path,
            'has_image': bool(self.test_image_path),
            'basic_info': self.get_basic_info()
        }

    @classmethod
    def get_weights_by_part_material(cls, part_name, material_name):
        """根据零件和材料获取克重列表"""
        weights = db.session.query(cls.weight).filter(
            and_(cls.part_name == part_name, cls.material_name == material_name)
        ).distinct().order_by(cls.weight).all()
        return [float(w[0]) for w in weights]

    @classmethod
    def get_data_by_conditions(cls, part_name, material_name, weight):
        """根据条件获取数据"""
        return cls.query.filter(
            and_(
                cls.part_name == part_name,
                cls.material_name == material_name,
                cls.weight == weight
            )
        ).first()

    @classmethod
    def get_multi_weight_data(cls, part_name, material_name, weights):
        """获取多克重数据"""
        return cls.query.filter(
            and_(
                cls.part_name == part_name,
                cls.material_name == material_name,
                cls.weight.in_(weights)
            )
        ).order_by(cls.weight).all()
